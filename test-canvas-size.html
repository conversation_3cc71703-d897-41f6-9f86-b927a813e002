<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Size Test</title>
    <style>
        .container {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 800px;
            height: 600px;
            background-color: #f5f7fa;
            margin: 20px auto;
            position: relative;
        }
        
        .canvas-wrapper {
            position: relative;
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .original-image {
            display: block;
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            pointer-events: none;
        }
        
        .mask-canvas {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 100%;
            max-height: 100%;
            cursor: crosshair;
            z-index: 1;
            pointer-events: auto;
            border: 2px solid red; /* 用于调试，显示canvas边界 */
        }
        
        .info {
            margin: 20px;
            padding: 10px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="info">
        <h3>Canvas与Image尺寸测试</h3>
        <p>红色边框显示canvas的边界，应该与图片完全重合</p>
        <div id="size-info"></div>
    </div>
    
    <div class="container">
        <div class="canvas-wrapper">
            <img src="https://via.placeholder.com/400x300/4CAF50/white?text=Test+Image" 
                 alt="测试图片" 
                 class="original-image" 
                 id="testImage" />
            <canvas id="testCanvas" class="mask-canvas"></canvas>
        </div>
    </div>

    <script>
        function initCanvas() {
            const img = document.getElementById('testImage');
            const canvas = document.getElementById('testCanvas');
            const sizeInfo = document.getElementById('size-info');
            
            function updateCanvasSize() {
                const displayWidth = img.clientWidth;
                const displayHeight = img.clientHeight;
                
                if (displayWidth === 0 || displayHeight === 0) {
                    setTimeout(updateCanvasSize, 50);
                    return;
                }
                
                // 设置canvas的CSS尺寸与图片显示尺寸完全相同
                canvas.style.width = `${displayWidth}px`;
                canvas.style.height = `${displayHeight}px`;
                
                // 设置canvas的实际尺寸
                const dpr = window.devicePixelRatio || 1;
                canvas.width = displayWidth * dpr;
                canvas.height = displayHeight * dpr;
                
                // 缩放上下文以匹配设备像素比
                const ctx = canvas.getContext('2d');
                ctx.scale(dpr, dpr);
                
                // 更新信息显示
                sizeInfo.innerHTML = `
                    <strong>图片显示尺寸:</strong> ${displayWidth} x ${displayHeight}px<br>
                    <strong>Canvas CSS尺寸:</strong> ${canvas.style.width} x ${canvas.style.height}<br>
                    <strong>Canvas实际尺寸:</strong> ${canvas.width} x ${canvas.height}px<br>
                    <strong>设备像素比:</strong> ${dpr}
                `;
                
                console.log('Canvas尺寸已更新:', {
                    imageDisplay: { width: displayWidth, height: displayHeight },
                    canvasCSS: { width: canvas.style.width, height: canvas.style.height },
                    canvasActual: { width: canvas.width, height: canvas.height },
                    dpr: dpr
                });
            }
            
            if (img.complete) {
                requestAnimationFrame(updateCanvasSize);
            } else {
                img.onload = () => requestAnimationFrame(updateCanvasSize);
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', initCanvas);
        
        // 窗口大小改变时重新调整
        window.addEventListener('resize', initCanvas);
    </script>
</body>
</html>
